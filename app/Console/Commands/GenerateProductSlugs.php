<?php

namespace App\Console\Commands;

use App\Models\Product;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class GenerateProductSlugs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'products:generate-slugs {--force : Force regenerate slugs for products that already have slugs}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate slugs for existing products that don\'t have slugs';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting slug generation for products...');

        $query = Product::query();
        
        if (!$this->option('force')) {
            $query->whereNull('slug');
        }

        $products = $query->whereNotNull('title_en')->get();

        if ($products->isEmpty()) {
            $this->info('No products found that need slug generation.');
            return;
        }

        $this->info("Found {$products->count()} products to process.");

        $bar = $this->output->createProgressBar($products->count());
        $bar->start();

        $updated = 0;
        $skipped = 0;

        foreach ($products as $product) {
            try {
                if (empty($product->title_en)) {
                    $this->warn("\nSkipping product ID {$product->id}: No title_en found");
                    $skipped++;
                    continue;
                }

                $slug = Product::generateUniqueSlug($product->title_en, $product->id);
                $product->slug = $slug;
                $product->save();
                
                $updated++;
            } catch (\Exception $e) {
                $this->error("\nError processing product ID {$product->id}: " . $e->getMessage());
                $skipped++;
            }

            $bar->advance();
        }

        $bar->finish();

        $this->newLine(2);
        $this->info("Slug generation completed!");
        $this->info("Updated: {$updated} products");
        
        if ($skipped > 0) {
            $this->warn("Skipped: {$skipped} products");
        }
    }
}
