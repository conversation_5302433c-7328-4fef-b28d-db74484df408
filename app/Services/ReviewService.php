<?php

namespace App\Services;

use App\Models\Review;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ReviewService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = Review::query();

        // Select specific columns
        $query->select(['*']);

        // Filter by authenticated user for user reviews
        if ($request->has('my_reviews') && $request->my_reviews) {
            $query->where('user_id', Auth::id());
        }

        // Load relationships
        $query->with([
            'user:id,name,email',
            'product:id,title_en,title_ar',
            'vendor:id,vendor_display_name_en,vendor_display_name_ar',
            'order:id,order_number',
            'attachments'
        ]);

        // Sorting
        $this->applySorting($query, $request);

        // Filtering
        $filters = [
            'product_id' => '=',
            'vendor_id' => '=',
            'user_id' => '=',
            'order_id' => '=',
            'rating' => '=',
            'is_approved' => '=',
            'is_visible' => '='
        ];
        $this->applyFilters($query, $request, $filters);

        // Special filters
        if ($request->has('min_rating')) {
            $query->withMinRating($request->min_rating);
        }

        if ($request->has('review_type')) {
            if ($request->review_type === 'product') {
                $query->whereNotNull('product_id');
            } elseif ($request->review_type === 'vendor') {
                $query->whereNotNull('vendor_id');
            }
        }

        if ($request->has('status')) {
            switch ($request->status) {
                case 'published':
                    $query->published();
                    break;
                case 'pending':
                    $query->pending();
                    break;
                case 'approved':
                    $query->approved();
                    break;
            }
        }

        // Searching
        $searchKeys = ['comment'];
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareReviewData($request);

        // Check for duplicate reviews
        if (isset($data['product_id']) && $data['product_id']) {
            $exists = Review::userHasReviewedProduct($data['user_id'], $data['product_id']);
            if ($exists) {
                throw new \Exception('You have already reviewed this product');
            }
        }

        if (isset($data['vendor_id']) && $data['vendor_id']) {
            $exists = Review::userHasReviewedVendor($data['user_id'], $data['vendor_id']);
            if ($exists) {
                throw new \Exception('You have already reviewed this vendor');
            }
        }

        // Create the review
        $review = Review::create($data);

        // Handle image URLs if present
        if ($request->has('images') && !empty($request->images)) {
            $this->handleImagePatchs($request, $review);
        }

        // Load the review with attachments for response
        $review->load('attachments');

        return new \App\Http\Resources\Review\ReviewResource($review);
    }

    public function show(int $id): Review
    {
        return Review::with([
            'user:id,name,email',
            'product:id,title_en,title_ar',
            'vendor:id,vendor_display_name_en,vendor_display_name_ar',
            'order:id,order_number',
            'attachments'
        ])->findOrFail($id);
    }

    public function update($request, int $id)
    {
        $review = Review::findOrFail($id);

        // Check if user can edit this review
        if (!$review->canBeEditedBy(Auth::id())) {
            throw new \Exception('You cannot edit this review');
        }

        $updateData = $this->prepareReviewData($request, false);

        // Remove user_id from update data to prevent changing ownership
        unset($updateData['user_id']);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $review->update($updateData);

        // Handle image URLs if present
        if ($request->has('images')) {
            // Remove existing attachments
            $review->attachments()->delete();

            // Add new images if provided
            if (!empty($request->images)) {
                $this->handleImagePatchs($request, $review);
            }
        }

        // Load the review with attachments for response
        $review->load('attachments');

        return new \App\Http\Resources\Review\ReviewResource($review);
    }

    public function destroy(int $id): bool
    {
        $review = Review::findOrFail($id);
        
        // Check if user can delete this review
        if (!$review->canBeEditedBy(Auth::id())) {
            throw new \Exception('You cannot delete this review');
        }

        return $review->delete();
    }

    public function getProductReviews($productId, $request)
    {
        $query = Review::forProduct($productId)->published();

        // Load relationships
        $query->with(['user:id,name', 'attachments']);

        // Sorting
        $this->applySorting($query, $request);

        // Rating filter
        if ($request->has('rating')) {
            $query->withRating($request->rating);
        }

        if ($request->has('min_rating')) {
            $query->withMinRating($request->min_rating);
        }

        $reviews = $this->paginateOrGet($query, $request);

        // Add purchase validation for authenticated users
        if (Auth::check()) {
            $hasPurchased = Review::userHasPurchasedProduct(Auth::id(), $productId);

            // Add has_purchased field to the response
            if (is_array($reviews) || $reviews instanceof \Illuminate\Support\Collection) {
                // For non-paginated results
                $reviewsData = $reviews;
                $meta = ['has_purchased' => $hasPurchased];
            } else {
                // For paginated results
                $reviewsData = $reviews->toArray();
                $reviewsData['has_purchased'] = $hasPurchased;
                return $reviewsData;
            }

            return [
                'data' => $reviewsData,
                'has_purchased' => $hasPurchased
            ];
        }

        return $reviews;
    }

    public function getVendorReviews($vendorId, $request)
    {
        $query = Review::forVendor($vendorId)->published();

        // Load relationships
        $query->with(['user:id,name']);

        // Sorting
        $this->applySorting($query, $request);

        // Rating filter
        if ($request->has('rating')) {
            $query->withRating($request->rating);
        }

        if ($request->has('min_rating')) {
            $query->withMinRating($request->min_rating);
        }

        return $this->paginateOrGet($query, $request);
    }

    public function getReviewSummary($targetType, $targetId)
    {
        $query = Review::published();
        
        if ($targetType === 'product') {
            $query->forProduct($targetId);
        } elseif ($targetType === 'vendor') {
            $query->forVendor($targetId);
        } else {
            throw new \Exception('Invalid target type');
        }

        $totalReviews = $query->count();
        $averageRating = $query->avg('rating') ?? 0;
        
        // Rating distribution
        $ratingDistribution = [];
        for ($i = 1; $i <= 5; $i++) {
            $count = (clone $query)->withRating($i)->count();
            $ratingDistribution[$i] = [
                'count' => $count,
                'percentage' => $totalReviews > 0 ? round(($count / $totalReviews) * 100, 1) : 0
            ];
        }

        return [
            'total_reviews' => $totalReviews,
            'average_rating' => round($averageRating, 1),
            'rating_distribution' => $ratingDistribution,
        ];
    }

    public function bulkApprove(array $ids)
    {
        $updated = Review::whereIn('id', $ids)->update(['is_approved' => true]);
        
        return [
            'approved_count' => $updated,
            'requested_count' => count($ids),
        ];
    }

    public function bulkReject(array $ids)
    {
        $updated = Review::whereIn('id', $ids)->update(['is_approved' => false]);
        
        return [
            'rejected_count' => $updated,
            'requested_count' => count($ids),
        ];
    }

    public function bulkHide(array $ids)
    {
        $updated = Review::whereIn('id', $ids)->update(['is_visible' => false]);
        
        return [
            'hidden_count' => $updated,
            'requested_count' => count($ids),
        ];
    }

    public function bulkShow(array $ids)
    {
        $updated = Review::whereIn('id', $ids)->update(['is_visible' => true]);
        
        return [
            'shown_count' => $updated,
            'requested_count' => count($ids),
        ];
    }

    public function bulkDelete(array $ids)
    {
        $deleted = Review::whereIn('id', $ids)->delete();
        
        return [
            'deleted_count' => $deleted,
            'requested_count' => count($ids),
        ];
    }

    private function prepareReviewData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        $fillable = (new Review())->getFillable();
        $data = array_intersect_key($validated, array_flip($fillable));

        if ($isNew) {
            $data['user_id'] = Auth::id();
            $data['is_approved'] = false; // Reviews need approval by default
            $data['is_visible'] = true;
            $data['created_at'] = now();
        }

        return $data;
    }

    /**
     * Handle image URLs for a review.
     */
    private function handleImagePatchs(Request $request, Review $review)
    {
        if (!$request->has('images') || empty($request->images)) {
            return;
        }

        foreach ($request->images as $imagePath) {
            try {
                // Create attachment record with just the path
                $review->attachments()->create([
                    'file_path' => $imagePath,
                ]);
            } catch (\Exception $e) {
                \Log::error('Failed to process review image path: ' . $e->getMessage());
                // Continue with other images
            }
        }
    }
}
