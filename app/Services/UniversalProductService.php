<?php

namespace App\Services;

use App\Models\Product;
use App\Traits\HelperTrait;
use Illuminate\Http\Request;

class UniversalProductService
{
    use HelperTrait;

    /**
     * Get products with comprehensive filtering and pagination
     * Works for category pages, subcategory pages, brand pages, search pages
     */
    public function getProducts(Request $request): array
    {
        $query = $this->buildBaseProductQuery();
        
        // Apply all possible filters
        $this->applyFilters($query, $request);
        
        // Apply search if provided
        $this->applySearch($query, $request);
        
        // Apply sorting
        $this->applySorting($query, $request);
        
        // Apply pagination
        return $this->paginateProducts($query, $request);
    }

    /**
     * Build base product query with common conditions
     */
    private function buildBaseProductQuery()
    {
        return Product::where('is_active', true)
            ->where('is_approved', true)
            ->select([
                'id','uuid', 'slug', 'title_en', 'title_ar', 'short_name',
                'regular_price', 'offer_price', 'brand_id',
                'user_group_id', 'country_of_origin', 'category_id',
                'sub_category_id', 'created_at'
            ])
            ->with([
                'productMedia' => function ($query) {
                    $query->where('is_primary', true)
                          ->select('id', 'product_id', 'path', 'alt_text');
                },
                'brand:id,name_en,name_ar',
                'userGroup:id,value_en,value_ar',
                'countryOfOrigin:id,value_en,value_ar'
            ]);
    }

    /**
     * Apply comprehensive filters based on request parameters
     */
    private function applyFilters($query, Request $request): void
    {
        // Category filter
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->input('category_id'));
        }

        // Subcategory filter
        if ($request->filled('subcategory_id')) {
            $query->where('sub_category_id', $request->input('subcategory_id'));
        }

        // Brand filter
        if ($request->filled('brand_id')) {
            if (is_array($request->input('brand_id'))) {
                $query->whereIn('brand_id', $request->input('brand_id'));
            } else {
                $query->where('brand_id', $request->input('brand_id'));
            }
        }

        // User group filter
        if ($request->filled('user_group_id')) {
            if (is_array($request->input('user_group_id'))) {
                $query->whereIn('user_group_id', $request->input('user_group_id'));
            } else {
                $query->where('user_group_id', $request->input('user_group_id'));
            }
        }

        // Country of origin filter
        if ($request->filled('country_of_origin_id')) {
            if (is_array($request->input('country_of_origin_id'))) {
                $query->whereIn('country_of_origin', $request->input('country_of_origin_id'));
            } else {
                $query->where('country_of_origin', $request->input('country_of_origin_id'));
            }
        }

        // Price range filters
        if ($request->filled('min_price')) {
            $query->where('regular_price', '>=', $request->input('min_price'));
        }

        if ($request->filled('max_price')) {
            $query->where('regular_price', '<=', $request->input('max_price'));
        }

        // Additional filters for comprehensive support
        if ($request->filled('formulation_id')) {
            $query->where('formulation_id', $request->input('formulation_id'));
        }

        if ($request->filled('flavour_id')) {
            $query->where('flavour_id', $request->input('flavour_id'));
        }

        if ($request->filled('storage_conditions_id')) {
            $query->where('storage_conditions', $request->input('storage_conditions_id'));
        }

        if ($request->filled('is_returnable_id')) {
            $query->where('is_returnable', $request->input('is_returnable_id'));
        }

        if ($request->filled('warranty_id')) {
            $query->where('warranty', $request->input('warranty_id'));
        }

        // Boolean filters
        if ($request->has('is_vegan')) {
            $query->where('is_vegan', filter_var($request->input('is_vegan'), FILTER_VALIDATE_BOOLEAN));
        }

        if ($request->has('is_vegetarian')) {
            $query->where('is_vegetarian', filter_var($request->input('is_vegetarian'), FILTER_VALIDATE_BOOLEAN));
        }

        if ($request->has('is_halal')) {
            $query->where('is_halal', filter_var($request->input('is_halal'), FILTER_VALIDATE_BOOLEAN));
        }
    }

    /**
     * Apply search functionality
     */
    private function applySearch($query, Request $request): void
    {
        $searchValue = $request->input('search');

        if ($searchValue) {
            $query->where(function ($query) use ($searchValue) {
                $searchTerm = '%' . strtolower($searchValue) . '%';

                $query->whereRaw('LOWER(title_en) LIKE ?', [$searchTerm])
                      ->orWhereRaw('LOWER(title_ar) LIKE ?', [$searchTerm])
                      ->orWhereRaw('LOWER(short_name) LIKE ?', [$searchTerm])
                      ->orWhereRaw('LOWER(system_sku) LIKE ?', [$searchTerm])
                      ->orWhereRaw('LOWER(vendor_sku) LIKE ?', [$searchTerm])
                      ->orWhereRaw('LOWER(barcode) LIKE ?', [$searchTerm]);
            });
        }
    }

    /**
     * Apply sorting based on request parameters
     */
    private function applySorting($query, Request $request): void
    {
        $sortBy = $request->input('sort_by', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');

        // Validate sort fields
        $allowedSortFields = [
            'created_at', 'title_en', 'title_ar', 'regular_price', 
            'offer_price', 'short_name'
        ];

        if (in_array($sortBy, $allowedSortFields)) {
            $query->orderBy($sortBy, $sortOrder === 'asc' ? 'asc' : 'desc');
        } else {
            $query->orderBy('created_at', 'desc');
        }
    }

    /**
     * Paginate products and format response
     */
    private function paginateProducts($query, Request $request): array
    {
        $perPage = $request->input('per_page', 10);
        $perPage = min($perPage, 50); // Enforce max limit

        $products = $query->paginate($perPage);

        return [
            'data' => $products->getCollection()->map(function ($product) {
                return [
                    'id' => $product->id,
                    'uuid' => $product->uuid,
                    'title_en' => $product->title_en,
                    'title_ar' => $product->title_ar,
                    'short_name' => $product->short_name,
                    'regular_price' => $product->regular_price,
                    'offer_price' => $product->offer_price,
                    'primary_image' => $product->productMedia->first() ? [
                        'path_url' => $product->productMedia->first()->path_url,
                        'alt_text' => $product->productMedia->first()->alt_text,
                    ] : null,
                    'brand' => $product->brand ? [
                        'id' => $product->brand->id,
                        'name_en' => $product->brand->name_en,
                        'name_ar' => $product->brand->name_ar,
                    ] : null,
                    'user_group' => $product->userGroup ? [
                        'id' => $product->userGroup->id,
                        'value_en' => $product->userGroup->value_en,
                        'value_ar' => $product->userGroup->value_ar,
                    ] : null,
                    'country_of_origin' => $product->countryOfOrigin ? [
                        'id' => $product->countryOfOrigin->id,
                        'value_en' => $product->countryOfOrigin->value_en,
                        'value_ar' => $product->countryOfOrigin->value_ar,
                    ] : null,
                ];
            }),
            'current_page' => $products->currentPage(),
            'per_page' => $products->perPage(),
            'total_items' => $products->total(),
            'total_pages' => $products->lastPage(),
            'has_more_pages' => $products->hasMorePages(),
        ];
    }
}
