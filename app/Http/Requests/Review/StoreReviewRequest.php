<?php

namespace App\Http\Requests\Review;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class StoreReviewRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'product_id' => 'nullable|exists:products,id|required_without:vendor_id',
            'vendor_id' => 'nullable|exists:vendors,id|required_without:product_id',
            'order_id' => 'nullable|exists:orders,id',
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000',
            'images' => 'nullable|array|max:5',
            'images.*' => 'required|string',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'product_id.required_without' => 'Either product or vendor must be specified',
            'product_id.exists' => 'Selected product does not exist',
            'vendor_id.required_without' => 'Either product or vendor must be specified',
            'vendor_id.exists' => 'Selected vendor does not exist',
            'order_id.exists' => 'Selected order does not exist',
            'rating.required' => 'Rating is required',
            'rating.integer' => 'Rating must be a number',
            'rating.min' => 'Rating must be at least 1 star',
            'rating.max' => 'Rating cannot exceed 5 stars',
            'comment.max' => 'Comment cannot exceed 1000 characters',
            'images.array' => 'Images must be provided as an array',
            'images.max' => 'You can upload a maximum of 5 images',
            'images.*.required' => 'Each image URL is required',
            'images.*.string' => 'Each image must be a valid URL string'
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $data = $validator->getData();

            // Ensure at least one target (product or vendor) is specified
            if (empty($data['product_id']) && empty($data['vendor_id'])) {
                $validator->errors()->add('target', 'You must specify either a product or vendor to review');
            }

            // Ensure both product and vendor are not specified at the same time
            if (!empty($data['product_id']) && !empty($data['vendor_id'])) {
                $validator->errors()->add('target', 'You cannot review both a product and vendor in the same review');
            }

            // Validate image uploads for product reviews only (verified buyers)
            if (!empty($data['images']) && !empty($data['product_id'])) {
                $userId = Auth::id();
                $productId = $data['product_id'];

                if ($userId && $productId) {
                    // Check if user has purchased the product
                    $hasPurchased = \App\Models\Review::userHasPurchasedProduct($userId, $productId);

                    if (!$hasPurchased) {
                        $validator->errors()->add('images', 'Only verified buyers who have purchased this product can upload images with their reviews');
                    }
                }
            }
        });
    }
}
