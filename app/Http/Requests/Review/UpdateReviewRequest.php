<?php

namespace App\Http\Requests\Review;

use Illuminate\Foundation\Http\FormRequest;

class UpdateReviewRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'rating' => 'sometimes|required|integer|min:1|max:5',
            'comment' => 'sometimes|nullable|string|max:1000',
            'images' => 'sometimes|nullable|array|max:5',
            'images.*' => 'required|string',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'rating.required' => 'Rating is required',
            'rating.integer' => 'Rating must be a number',
            'rating.min' => 'Rating must be at least 1 star',
            'rating.max' => 'Rating cannot exceed 5 stars',
            'comment.max' => 'Comment cannot exceed 1000 characters',
            'images.array' => 'Images must be provided as an array',
            'images.max' => 'You can upload a maximum of 5 images',
            'images.*.string' => 'Each image must be a valid URL string',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $data = $validator->getData();

            // Only validate purchase if images are being updated
            if (!empty($data['images'])) {
                // Get the review being updated
                $reviewId = $this->route()->parameter('id');
                $review = \App\Models\Review::find($reviewId);

                if (!$review) {
                    $validator->errors()->add('review', 'Review not found');
                    return;
                }

                // Check if user has purchased the product (only for product reviews)
                if ($review->product_id) {
                    $hasPurchased = \App\Models\OrderItem::whereHas('order', function ($query) {
                        $query->where('user_id', \Illuminate\Support\Facades\Auth::id())
                              ->where('payment_status', 'paid');
                    })->where('product_id', $review->product_id)->exists();

                    if (!$hasPurchased) {
                        $validator->errors()->add('images', 'You can only upload images for products you have purchased');
                    }
                }
            }
        });
    }
}
