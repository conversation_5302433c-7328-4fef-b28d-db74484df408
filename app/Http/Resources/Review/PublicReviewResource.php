<?php

namespace App\Http\Resources\Review;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PublicReviewResource extends JsonResource
{
    /**
     * Transform the resource into an array for public consumption.
     * This resource is used for displaying reviews to all users (authenticated and unauthenticated).
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'rating' => $this->rating,
            'comment' => $this->comment,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),

            // User information (limited for privacy)
            'user' => $this->whenLoaded('user', function () {
                return [
                    'id' => $this->user->id,
                    'name' => $this->user->name,
                ];
            }),

            // Review attachments (images)
            'images' => $this->whenLoaded('attachments', function () {
                return $this->attachments->map(function ($attachment) {
                    return [
                        'id' => $attachment->id,
                        'url' => $attachment->file_url,
                        'thumbnail_url' => $attachment->thumbnail_url,
                        'original_name' => $attachment->original_name,
                        'file_size' => $attachment->formatted_file_size,
                        'width' => $attachment->width,
                        'height' => $attachment->height,
                        'is_primary' => $attachment->is_primary,
                        'sort_order' => $attachment->sort_order,
                    ];
                });
            }),

            // Computed attributes
            'formatted_rating' => $this->formatted_rating,
        ];
    }
}
