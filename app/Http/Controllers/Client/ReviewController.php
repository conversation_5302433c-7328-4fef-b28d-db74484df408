<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Http\Requests\Review\StoreReviewRequest;
use App\Http\Requests\Review\UpdateReviewRequest;
use App\Http\Resources\Review\PublicReviewResource;
use App\Services\ReviewService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class ReviewController extends Controller
{
    use HelperTrait;

    private $service;

    public function __construct(ReviewService $service)
    {
        $this->service = $service;
    }

    /**
     * Display a listing of the user's reviews.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // Force my_reviews filter for authenticated users
            $request->merge(['my_reviews' => true]);
            
            $data = $this->service->index($request);

            return $this->successResponse($data, 'Reviews retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Store a newly created review.
     */
    public function store(StoreReviewRequest $request): JsonResponse
    {
        try {
            $resource = $this->service->store($request);

            return $this->successResponse($resource, 'Review submitted successfully!', Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to submit review', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Display the specified review.
     */
    public function show(int $id): JsonResponse
    {
        try {
            $resource = $this->service->show($id);

            // Check if user owns this review
            if ($resource->user_id !== Auth::id()) {
                return $this->errorResponse('Unauthorized', 'You can only view your own reviews', Response::HTTP_FORBIDDEN);
            }

            return $this->successResponse($resource, 'Review retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve review', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Update the specified review.
     */
    public function update(UpdateReviewRequest $request, int $id): JsonResponse
    {
        try {
            $resource = $this->service->update($request, $id);

            return $this->successResponse($resource, 'Review updated successfully!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to update review', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove the specified review.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $this->service->destroy($id);

            return $this->successResponse(null, 'Review deleted successfully!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to delete review', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get reviews for a specific product.
     */
    public function getProductReviews(int $productId, Request $request): JsonResponse
    {
        try {
            $result = $this->service->getProductReviews($productId, $request);

            // Handle the response structure based on whether user is authenticated
            if (Auth::check() && isset($result['has_purchased'])) {
                // For authenticated users, include purchase validation
                $reviews = $result['data'];
                $hasPurchased = $result['has_purchased'];

                // Transform reviews using the public resource
                if (isset($reviews['data'])) {
                    // Paginated response
                    $reviews['data'] = PublicReviewResource::collection(collect($reviews['data']));
                    $reviews['has_purchased'] = $hasPurchased;
                    $data = $reviews;
                } else {
                    // Non-paginated response
                    $data = [
                        'data' => PublicReviewResource::collection(collect($reviews)),
                        'has_purchased' => $hasPurchased
                    ];
                }
            } else {
                // For unauthenticated users, just return the reviews
                if (isset($result['data'])) {
                    // Paginated response
                    $result['data'] = PublicReviewResource::collection(collect($result['data']));
                    $data = $result;
                } else {
                    // Non-paginated response
                    $data = PublicReviewResource::collection(collect($result));
                }
            }

            return $this->successResponse($data, 'Product reviews retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve product reviews', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get reviews for a specific vendor.
     */
    public function getVendorReviews(int $vendorId, Request $request): JsonResponse
    {
        try {
            $data = $this->service->getVendorReviews($vendorId, $request);

            return $this->successResponse($data, 'Vendor reviews retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve vendor reviews', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get review summary for a product or vendor.
     */
    public function getReviewSummary(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'target_type' => 'required|in:product,vendor',
                'target_id' => 'required|integer',
            ]);

            $data = $this->service->getReviewSummary($request->target_type, $request->target_id);

            return $this->successResponse($data, 'Review summary retrieved successfully!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 'Invalid request parameters', Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve review summary', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
