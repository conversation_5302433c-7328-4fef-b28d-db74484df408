<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Http\Requests\Cart\AddToCartRequest;
use App\Http\Requests\Cart\ApplyCouponRequest;
use App\Http\Requests\Cart\BulkUpdateCartItemsRequest;
use App\Http\Requests\Cart\CreateCartRequest;
use App\Http\Requests\Cart\GetCartRequest;
use App\Http\Requests\Cart\TokenBasedCartRequest;
use App\Http\Requests\Cart\UpdateCartItemRequest;
use App\Http\Resources\Cart\CartItemResource;
use App\Http\Resources\Cart\CartResource;
use App\Models\CartItem;
use App\Models\ShoppingCart;
use App\Services\CartService;
use App\Services\CartValidationService;
use App\Services\InventoryReservationService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CartController extends Controller
{
    use HelperTrait;

    protected CartService $cartService;
    protected CartValidationService $validationService;
    protected InventoryReservationService $reservationService;

    public function __construct(
        CartService $cartService,
        CartValidationService $validationService,
        InventoryReservationService $reservationService
    ) {
        $this->cartService = $cartService;
        $this->validationService = $validationService;
        $this->reservationService = $reservationService;
    }

    /**
     * Create a new cart.
     */
    public function createCart(CreateCartRequest $request): JsonResponse
    {
        try {
            $cart = $this->cartService->createCart($request->validated());

            return $this->successResponse(
                new CartResource($cart),
                'Cart created successfully!',
                Response::HTTP_CREATED
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to create cart',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get cart by UUID with token-based authentication (React-friendly).
     */
    public function getCart(TokenBasedCartRequest $request): JsonResponse
    {
        try {
            $cartId = $request->validated()['cartId'];
            $cartToken = $request->validated()['cart_token'] ?? null;
            
            $cart = ShoppingCart::where('uuid', $cartId)
                ->with(['items.product', 'items.vendor', 'items.variant'])
                ->first();

            if (!$cart) {
                return $this->errorResponse(
                    'Cart not found',
                    'Cart not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            // Token-based authentication for React apps
            if (!$this->canAccessCart($cart, $cartToken)) {
                return $this->errorResponse(
                    'Unauthorized access to cart',
                    'Unauthorized',
                    Response::HTTP_FORBIDDEN
                );
            }

            return $this->successResponse(
                new CartResource($cart),
                'Cart retrieved successfully!',
                Response::HTTP_OK
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Add item to cart.
     */
    public function addItem(string $cartId, AddToCartRequest $request): JsonResponse
    {
        try {
            $cart = $this->findCartByUuid($cartId);
            
            if (!$cart) {
                return $this->errorResponse(
                    'Cart not found',
                    'Cart not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            $cartItem = $this->cartService->addItem($cart, $request->validatedWithComputed());

            return $this->successResponse(
                new CartItemResource($cartItem),
                'Item added to cart successfully!',
                Response::HTTP_CREATED
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to add item to cart',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Update cart item.
     */
    public function updateItem(string $cartId, int $itemId, UpdateCartItemRequest $request): JsonResponse
    {
        try {
            $cart = $this->findCartByUuid($cartId);
            
            if (!$cart) {
                return $this->errorResponse(
                    'Cart not found',
                    'Cart not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            $cartItem = CartItem::where('id', $itemId)
                ->where('cart_id', $cart->id)
                ->first();

            if (!$cartItem) {
                return $this->errorResponse(
                    'Cart item not found',
                    'Cart item not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            if ($request->shouldDeleteItem()) {
                $this->cartService->removeItem($cartItem);
                
                return $this->successResponse(
                    null,
                    'Item removed from cart successfully!',
                    Response::HTTP_OK
                );
            }

            $updatedItem = $this->cartService->updateItem($cartItem, $request->validated());

            return $this->successResponse(
                new CartItemResource($updatedItem),
                'Cart item updated successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to update cart item',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Remove item from cart.
     */
    public function removeItem(string $cartId, int $itemId): JsonResponse
    {
        try {
            $cart = $this->findCartByUuid($cartId);
            
            if (!$cart) {
                return $this->errorResponse(
                    'Cart not found',
                    'Cart not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            $cartItem = CartItem::where('id', $itemId)
                ->where('cart_id', $cart->id)
                ->first();

            if (!$cartItem) {
                return $this->errorResponse(
                    'Cart item not found',
                    'Cart item not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            $this->cartService->removeItem($cartItem);

            return $this->successResponse(
                null,
                'Item removed from cart successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to remove cart item',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Bulk update cart items.
     */
    public function bulkUpdateItems(string $cartId, BulkUpdateCartItemsRequest $request): JsonResponse
    {
        try {
            $cart = $this->findCartByUuid($cartId);
            
            if (!$cart) {
                return $this->errorResponse(
                    'Cart not found',
                    'Cart not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            $updatedItems = $this->cartService->bulkUpdateItems($cart, $request->validated()['items']);

            return $this->successResponse(
                CartItemResource::collection($updatedItems),
                'Cart items updated successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to update cart items',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Clear all items from cart.
     */
    public function clearCart(string $cartId): JsonResponse
    {
        try {
            $cart = $this->findCartByUuid($cartId);
            
            if (!$cart) {
                return $this->errorResponse(
                    'Cart not found',
                    'Cart not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            $this->cartService->clearCart($cart);

            return $this->successResponse(
                new CartResource($cart->fresh()),
                'Cart cleared successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to clear cart',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Apply coupon to cart.
     */
    public function applyCoupon(string $cartId, ApplyCouponRequest $request): JsonResponse
    {
        try {
            $cart = $this->findCartByUuid($cartId);
            
            if (!$cart) {
                return $this->errorResponse(
                    'Cart not found',
                    'Cart not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            $result = $this->cartService->applyCoupon($cart, $request->coupon_code);

            return $this->successResponse(
                array_merge($result, ['cart' => new CartResource($cart->fresh())]),
                'Coupon applied successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to apply coupon',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Remove coupon from cart.
     */
    public function removeCoupon(string $cartId, Request $request): JsonResponse
    {
        try {
            $request->validate([
                'coupon_code' => 'required|string|max:50',
            ]);

            $cart = $this->findCartByUuid($cartId);
            
            if (!$cart) {
                return $this->errorResponse(
                    'Cart not found',
                    'Cart not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            $this->cartService->removeCoupon($cart, $request->coupon_code);

            return $this->successResponse(
                new CartResource($cart->fresh()),
                'Coupon removed successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to remove coupon',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get vendor groups for multi-vendor checkout.
     */
    public function getVendorSplit(string $cartId): JsonResponse
    {
        try {
            $cart = $this->findCartByUuid($cartId);
            
            if (!$cart) {
                return $this->errorResponse(
                    'Cart not found',
                    'Cart not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            $vendorGroups = $this->cartService->getVendorGroups($cart);

            return $this->successResponse(
                $vendorGroups,
                'Vendor groups retrieved successfully!',
                Response::HTTP_OK
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Validate cart for checkout.
     */
    public function validateCart(string $cartId): JsonResponse
    {
        try {
            $cart = $this->findCartByUuid($cartId);
            
            if (!$cart) {
                return $this->errorResponse(
                    'Cart not found',
                    'Cart not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            $validation = $this->validationService->validateCartForCheckout($cart);

            return $this->successResponse(
                $validation,
                'Cart validation completed!',
                Response::HTTP_OK
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Merge carts.
     */
    public function mergeCarts(string $cartId, Request $request): JsonResponse
    {
        try {
            $request->validate([
                'source_cart_id' => 'required|string|exists:shopping_carts,uuid',
            ]);

            $targetCart = $this->findCartByUuid($cartId);
            $sourceCart = $this->findCartByUuid($request->source_cart_id);

            if (!$targetCart || !$sourceCart) {
                return $this->errorResponse(
                    'One or both carts not found',
                    'Cart not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            $mergedCart = $this->cartService->mergeCarts($targetCart, $sourceCart);

            return $this->successResponse(
                new CartResource($mergedCart),
                'Carts merged successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to merge carts',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        } catch (\Throwable $th) {
            return $this->errorResponse(
                $th->getMessage(),
                'Something went wrong',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Reserve inventory for cart items.
     */
    public function reserveInventory(string $cartId, Request $request): JsonResponse
    {
        try {
            $request->validate([
                'reservation_minutes' => 'nullable|integer|min:5|max:120', // 5 minutes to 2 hours
            ]);

            $cart = $this->findCartByUuid($cartId);
            if (!$cart) {
                return $this->errorResponse(
                    'Cart not found',
                    'Cart not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            $reservationMinutes = $request->reservation_minutes ?? 15;
            $result = $this->reservationService->reserveCartInventory($cart, $reservationMinutes);

            if (!$result['success']) {
                return $this->errorResponse(
                    'Some items could not be reserved',
                    'Inventory reservation failed',
                    Response::HTTP_CONFLICT,
                    $result
                );
            }

            return $this->successResponse(
                $result,
                'Inventory reserved successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to reserve inventory',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }
    }

    /**
     * Release inventory reservations for cart.
     */
    public function releaseReservations(string $cartId): JsonResponse
    {
        try {
            $cart = $this->findCartByUuid($cartId);
            if (!$cart) {
                return $this->errorResponse(
                    'Cart not found',
                    'Cart not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            $result = $this->reservationService->releaseCartReservations($cart);

            return $this->successResponse(
                $result,
                'Inventory reservations released successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to release reservations',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }
    }

    /**
     * Extend inventory reservations for cart.
     */
    public function extendReservations(string $cartId, Request $request): JsonResponse
    {
        try {
            $request->validate([
                'additional_minutes' => 'required|integer|min:5|max:60', // 5 minutes to 1 hour
            ]);

            $cart = $this->findCartByUuid($cartId);
            if (!$cart) {
                return $this->errorResponse(
                    'Cart not found',
                    'Cart not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            $result = $this->reservationService->extendCartReservations(
                $cart,
                $request->additional_minutes
            );

            return $this->successResponse(
                $result,
                'Inventory reservations extended successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to extend reservations',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }
    }

    /**
     * Get inventory availability for cart items.
     */
    public function getInventoryAvailability(string $cartId): JsonResponse
    {
        try {
            $cart = $this->findCartByUuid($cartId);
            if (!$cart) {
                return $this->errorResponse(
                    'Cart not found',
                    'Cart not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            $availability = [];
            foreach ($cart->items as $item) {
                $availableStock = $this->reservationService->getAvailableStock(
                    $item->product_id,
                    $item->variant_id
                );

                $availability[] = [
                    'cart_item_id' => $item->id,
                    'product_id' => $item->product_id,
                    'variant_id' => $item->variant_id,
                    'requested_quantity' => $item->quantity,
                    'available_stock' => $availableStock,
                    'is_available' => $availableStock >= $item->quantity,
                    'shortage' => max(0, $item->quantity - $availableStock),
                ];
            }

            return $this->successResponse(
                ['items' => $availability],
                'Inventory availability retrieved successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to get inventory availability',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }
    }

    /**
     * Check if user can access cart (token-based for React apps).
     */
    protected function canAccessCart(ShoppingCart $cart, ?string $cartToken = null): bool
    {
        // For authenticated users, check user ownership
        if (auth()->check()) {
            return $cart->user_id === auth()->id();
        }

        // For guest users with React apps, check cart token
        if ($cartToken && $cart->cart_token) {
            return hash_equals($cart->cart_token, $cartToken);
        }

        // Fallback to session-based check (for backward compatibility)
        if ($cart->session_id && session()->getId()) {
            return $cart->session_id === session()->getId();
        }

        // For guest carts without user_id, allow access if no token is required
        return !$cart->user_id;
    }

    /**
     * Find cart by UUID with ownership validation.
     */
    protected function findCartByUuid(string $cartId): ?ShoppingCart
    {
        $cart = ShoppingCart::where('uuid', $cartId)->first();

        if (!$cart) {
            return null;
        }

        // Check ownership
        if (auth()->check()) {
            return $cart->user_id === auth()->id() ? $cart : null;
        } else {
            return $cart->session_id === session()->getId() ? $cart : null;
        }
    }
}
