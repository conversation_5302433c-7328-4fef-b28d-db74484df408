<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class ShoppingCart extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'user_id',
        'session_id',
        'cart_token', // Add cart token for React apps
        'currency',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'shipping_amount',
        'total_amount',
        'status',
        'expires_at',
        'last_activity_at',
        'applied_coupons',
        'applied_discounts',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'shipping_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'expires_at' => 'datetime',
        'last_activity_at' => 'datetime',
        'applied_coupons' => 'array',
        'applied_discounts' => 'array',
        'metadata' => 'array',
    ];

    protected $appends = [
        'items_count',
        'total_quantity',
        'is_expired',
        'vendor_groups',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($cart) {
            if (empty($cart->uuid)) {
                $cart->uuid = Str::uuid();
            }
            // Generate cart token for guest carts (React app compatibility)
            if (empty($cart->cart_token) && empty($cart->user_id)) {
                $cart->cart_token = Str::random(64);
            }
            $cart->last_activity_at = now();
        });

        static::updating(function ($cart) {
            $cart->last_activity_at = now();
        });
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(CartItem::class, 'cart_id');
    }

    public function reservations(): HasMany
    {
        return $this->hasMany(CartReservation::class, 'cart_item_id');
    }

    public function abandonedCart(): HasMany
    {
        return $this->hasMany(AbandonedCart::class, 'cart_id');
    }

    // Accessors
    public function getItemsCountAttribute(): int
    {
        return $this->items()->count();
    }

    public function getTotalQuantityAttribute(): int
    {
        return $this->items()->sum('quantity');
    }

    public function getIsExpiredAttribute(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    public function getVendorGroupsAttribute(): array
    {
        return $this->items()
            ->with('vendor')
            ->get()
            ->groupBy('vendor_id')
            ->map(function ($items, $vendorId) {
                $vendor = $items->first()->vendor;
                return [
                    'vendor_id' => $vendorId,
                    'vendor_name' => $vendor->name ?? 'Unknown Vendor',
                    'items' => $items,
                    'subtotal' => $items->sum('total_price'),
                    'items_count' => $items->count(),
                    'total_quantity' => $items->sum('quantity'),
                ];
            })
            ->values()
            ->toArray();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeForSession($query, $sessionId)
    {
        return $query->where('session_id', $sessionId);
    }

    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now());
    }

    public function scopeNotExpired($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
                ->orWhere('expires_at', '>', now());
        });
    }

    // Business Logic Methods
    public function calculateTotals(): void
    {
        $this->subtotal = $this->items()->sum('total_price');
        $this->tax_amount = $this->calculateTax();
        $this->shipping_amount = $this->calculateShipping();
        $this->total_amount = $this->subtotal + $this->tax_amount + $this->shipping_amount - $this->discount_amount;
        $this->save();
    }

    public function isEmpty(): bool
    {
        return $this->items()->count() === 0;
    }

    public function hasExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    public function markAsAbandoned(): void
    {
        $this->update(['status' => 'abandoned']);
    }

    public function markAsConverted(): void
    {
        $this->update(['status' => 'converted']);
    }

    protected function calculateTax(): float
    {
        // Tax calculation logic will be implemented based on business rules
        return $this->items()->sum('tax_amount');
    }

    protected function calculateShipping(): float
    {
        // Shipping calculation logic will be implemented based on vendor rules
        return 0.00; // Placeholder
    }
}
