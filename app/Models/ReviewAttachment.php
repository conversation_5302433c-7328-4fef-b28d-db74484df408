<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReviewAttachment extends Model
{
    use HasFactory;

    protected $fillable = [
        'review_id',
        'file_path',
    ];



    /**
     * Get the review that owns this attachment.
     */
    public function review()
    {
        return $this->belongsTo(Review::class);
    }
}
