<?php

namespace Tests\Feature;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\Review;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Passport\Passport;
use Tests\TestCase;

class ProductReviewsApiTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $product;
    protected $vendor;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->vendor = Vendor::factory()->create();
        $this->product = Product::factory()->create([
            'vendor_id' => $this->vendor->id,
            'storage_conditions' => null,
            'country_of_origin' => null,
        ]);
    }

    private function createReview($overrides = [])
    {
        return Review::create(array_merge([
            'user_id' => User::factory()->create()->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'rating' => 4,
            'title' => 'Great product',
            'comment' => 'This is a great product, highly recommended!',
            'is_approved' => true,
            'is_visible' => true,
            'status' => 'approved',
            'verified_purchase' => true,
            'review_date' => now(),
        ], $overrides));
    }

    /** @test */
    public function it_returns_product_reviews_for_unauthenticated_users()
    {
        // Create some approved reviews
        for ($i = 0; $i < 3; $i++) {
            $this->createReview();
        }

        $response = $this->getJson("/api/client/reviews/product/{$this->product->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'optional',
                        'data' => [
                            '*' => [
                                'id',
                                'rating',
                                'comment',
                                'created_at',
                                'user' => [
                                    'id',
                                    'name'
                                ],
                                'formatted_rating'
                            ]
                        ],
                        'current_page',
                        'per_page',
                        'total_items',
                        'total_pages'
                    ]
                ]);

        // Should not include has_purchased field for unauthenticated users
        $response->assertJsonMissing(['has_purchased']);
    }

    /** @test */
    public function it_returns_product_reviews_with_purchase_validation_for_authenticated_users()
    {
        Passport::actingAs($this->user);

        // Create some approved reviews
        for ($i = 0; $i < 2; $i++) {
            $this->createReview();
        }

        $response = $this->getJson("/api/client/reviews/product/{$this->product->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'optional',
                        'data' => [
                            '*' => [
                                'id',
                                'rating',
                                'comment',
                                'created_at',
                                'user' => [
                                    'id',
                                    'name'
                                ],
                                'formatted_rating'
                            ]
                        ],
                        'current_page',
                        'per_page',
                        'total_items',
                        'total_pages',
                        'has_purchased'
                    ]
                ]);

        // Should include has_purchased field for authenticated users
        $response->assertJsonPath('data.has_purchased', false);
    }

    /** @test */
    public function it_returns_has_purchased_true_when_user_has_purchased_product()
    {
        Passport::actingAs($this->user);

        // Create a delivered order with the product
        $order = Order::create([
            'user_id' => $this->user->id,
            'vendor_id' => $this->vendor->id,
            'order_number' => 'ORD-' . time(),
            'payment_status' => 'paid',
            'fulfillment_status' => 'delivered',
            'total_amount' => 100.00,
            'status' => 'completed'
        ]);

        OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'product_title' => $this->product->title_en,
            'quantity' => 1,
            'price' => 100.00,
            'total' => 100.00,
            'base_price' => 100.00,
            'product_snapshot' => [
                'name' => $this->product->title_en,
                'price' => 100.00
            ]
        ]);

        // Create some approved reviews
        for ($i = 0; $i < 2; $i++) {
            $this->createReview();
        }

        $response = $this->getJson("/api/client/reviews/product/{$this->product->id}");

        $response->assertStatus(200)
                ->assertJsonPath('data.has_purchased', true);
    }

    /** @test */
    public function it_returns_has_purchased_false_when_user_has_not_purchased_product()
    {
        Passport::actingAs($this->user);

        // Create some approved reviews
        for ($i = 0; $i < 2; $i++) {
            $this->createReview();
        }

        $response = $this->getJson("/api/client/reviews/product/{$this->product->id}");

        $response->assertStatus(200)
                ->assertJsonPath('data.has_purchased', false);
    }

    /** @test */
    public function it_returns_has_purchased_false_when_order_is_not_delivered_or_shipped()
    {
        Passport::actingAs($this->user);

        // Create a pending order with the product
        $order = Order::create([
            'user_id' => $this->user->id,
            'vendor_id' => $this->vendor->id,
            'order_number' => 'ORD-' . time() . '-2',
            'payment_status' => 'paid',
            'fulfillment_status' => 'pending',
            'total_amount' => 100.00,
            'status' => 'processing'
        ]);

        OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'product_title' => $this->product->title_en,
            'quantity' => 1,
            'price' => 100.00,
            'total' => 100.00,
            'base_price' => 100.00,
            'product_snapshot' => [
                'name' => $this->product->title_en,
                'price' => 100.00
            ]
        ]);

        // Create some approved reviews
        for ($i = 0; $i < 2; $i++) {
            $this->createReview();
        }

        $response = $this->getJson("/api/client/reviews/product/{$this->product->id}");

        $response->assertStatus(200)
                ->assertJsonPath('data.has_purchased', false);
    }

    /** @test */
    public function it_only_shows_approved_and_visible_reviews()
    {
        // Create approved and visible reviews
        for ($i = 0; $i < 2; $i++) {
            $this->createReview();
        }

        // Create unapproved reviews
        $this->createReview([
            'is_approved' => false,
            'is_visible' => true,
            'status' => 'pending'
        ]);

        // Create invisible reviews
        $this->createReview([
            'is_approved' => true,
            'is_visible' => false
        ]);

        $response = $this->getJson("/api/client/reviews/product/{$this->product->id}");

        $response->assertStatus(200);
        
        // Should only return the 2 approved and visible reviews
        $this->assertCount(2, $response->json('data.data'));
    }

    /** @test */
    public function it_returns_empty_array_when_no_reviews_exist()
    {
        $response = $this->getJson("/api/client/reviews/product/{$this->product->id}");

        $response->assertStatus(200)
                ->assertJson([
                    'status' => true,
                    'data' => [
                        'data' => []
                    ]
                ]);
    }

    /** @test */
    public function it_returns_empty_array_for_non_existent_product()
    {
        $response = $this->getJson("/api/client/reviews/product/99999");

        $response->assertStatus(200)
                ->assertJson([
                    'status' => true,
                    'data' => [
                        'data' => []
                    ]
                ]);
    }
}
