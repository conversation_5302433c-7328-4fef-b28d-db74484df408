<?php

namespace Tests\Feature;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\Review;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ReviewUpdateImageUrlTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Product $product;
    private Vendor $vendor;
    private Review $review;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create();

        // Create test vendor
        $this->vendor = Vendor::factory()->create();

        // Create test product with proper factory overrides
        $this->product = Product::factory()->create([
            'vendor_id' => $this->vendor->id,
            'storage_conditions' => null,
            'country_of_origin' => null,
        ]);

        // Create a review
        $this->review = Review::factory()->create([
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'rating' => 4,
            'comment' => 'Original review comment',
            'is_approved' => false, // Reviews can only be edited if not approved
        ]);

        // Authenticate the user
        $this->actingAs($this->user, 'api');
    }

    /** @test */
    public function verified_buyer_can_update_review_with_image_urls()
    {
        // Create order and order item to establish purchase
        $order = Order::create([
            'user_id' => $this->user->id,
            'vendor_id' => $this->vendor->id,
            'order_number' => 'TEST-' . time(),
            'status' => 'completed',
            'payment_status' => 'paid',
            'total_amount' => 100.00,
            'currency' => 'AED',
        ]);

        OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'product_title' => $this->product->title_en,
            'quantity' => 1,
            'price' => 100.00,
            'base_price' => 100.00,
            'total' => 100.00,
            'product_snapshot' => [
                'name' => $this->product->title_en,
                'price' => 100.00,
            ],
        ]);

        $imagePaths = [
            '/images/updated1.jpg',
            '/images/updated2.png',
        ];

        $response = $this->putJson("/api/client/reviews/{$this->review->id}", [
            'rating' => 5,
            'comment' => 'Updated review comment',
            'images' => $imagePaths,
        ]);

        $response->assertStatus(200);
        
        // Check the response structure includes images
        $response->assertJsonStructure([
            'data' => [
                'id',
                'rating',
                'comment',
                'images' => [
                    '*' => [
                        'id',
                        'path',
                    ]
                ]
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertCount(2, $responseData['images']);
        $this->assertEquals('/images/updated1.jpg', $responseData['images'][0]['path']);
        $this->assertEquals('/images/updated2.png', $responseData['images'][1]['path']);
        
        // Also verify in database
        $this->review->refresh();
        $this->assertEquals(5, $this->review->rating);
        $this->assertEquals('Updated review comment', $this->review->comment);
        $this->assertEquals(2, $this->review->attachments()->count());
    }

    /** @test */
    public function user_cannot_update_review_with_images_if_not_purchased()
    {
        $imagePaths = ['/images/test.jpg'];

        $response = $this->putJson("/api/client/reviews/{$this->review->id}", [
            'rating' => 5,
            'comment' => 'Updated review comment',
            'images' => $imagePaths,
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['images']);
    }

    /** @test */
    public function user_can_update_review_without_images()
    {
        $response = $this->putJson("/api/client/reviews/{$this->review->id}", [
            'rating' => 5,
            'comment' => 'Updated review comment without images',
        ]);

        $response->assertStatus(200);
        
        $this->review->refresh();
        $this->assertEquals(5, $this->review->rating);
        $this->assertEquals('Updated review comment without images', $this->review->comment);
    }

    /** @test */
    public function user_can_remove_images_by_sending_empty_array()
    {
        // First add some images to the review
        $this->review->attachments()->create([
            'file_path' => '/images/old-image.jpg',
        ]);

        $this->assertEquals(1, $this->review->attachments()->count());

        $response = $this->putJson("/api/client/reviews/{$this->review->id}", [
            'rating' => 3,
            'comment' => 'Updated review without images',
            'images' => [], // Empty array to remove images
        ]);

        $response->assertStatus(200);
        
        $this->review->refresh();
        $this->assertEquals(0, $this->review->attachments()->count());
    }

    /** @test */
    public function image_update_validation_rules()
    {
        $response = $this->putJson("/api/client/reviews/{$this->review->id}", [
            'images' => ['not-a-string', 123, null], // Invalid image data
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['images']);
    }

    /** @test */
    public function cannot_update_more_than_5_images()
    {
        $imagePaths = [
            '/images/image1.jpg',
            '/images/image2.jpg',
            '/images/image3.jpg',
            '/images/image4.jpg',
            '/images/image5.jpg',
            '/images/image6.jpg', // 6th image should fail
        ];

        $response = $this->putJson("/api/client/reviews/{$this->review->id}", [
            'images' => $imagePaths,
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['images']);
    }

    /** @test */
    public function unauthenticated_user_cannot_update_review()
    {
        $this->app['auth']->forgetGuards();

        $response = $this->putJson("/api/client/reviews/{$this->review->id}", [
            'rating' => 5,
        ]);

        $response->assertStatus(401);
    }

    /** @test */
    public function user_cannot_update_other_users_review()
    {
        $otherUser = User::factory()->create();
        $this->actingAs($otherUser, 'api');

        $response = $this->putJson("/api/client/reviews/{$this->review->id}", [
            'rating' => 5,
        ]);

        $response->assertStatus(422);
    }
}
