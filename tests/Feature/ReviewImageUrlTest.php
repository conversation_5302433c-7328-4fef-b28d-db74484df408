<?php

namespace Tests\Feature;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\Review;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Passport\Passport;
use Tests\TestCase;

class ReviewImageUrlTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $vendor;
    protected $product;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data using factories
        $this->user = User::factory()->create();
        $this->vendor = Vendor::factory()->create();
        
        // Use the factory but override specific fields to avoid foreign key issues
        $this->product = Product::factory()->create([
            'user_id' => $this->user->id,
            'vendor_id' => $this->vendor->id,
            'is_active' => true,
            'is_approved' => true,
            'status' => 'submitted',
            'storage_conditions' => null,
            'country_of_origin' => null,
        ]);
    }

    public function test_verified_buyer_can_submit_review_with_image_urls()
    {
        // Create a completed order for the user with this product
        $this->createCompletedOrder();
        Passport::actingAs($this->user);

        $imagePaths = [
            '/images/product1.jpg',
            '/images/product2.png',
        ];

        $response = $this->postJson('/api/client/reviews', [
            'product_id' => $this->product->id,
            'rating' => 5,
            'comment' => 'Great product with images!',
            'images' => $imagePaths,
        ]);

        $response->assertStatus(201);
        
        // Verify review was created
        $this->assertDatabaseHas('reviews', [
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'rating' => 5,
            'comment' => 'Great product with images!',
        ]);

        // Verify attachments were created
        $review = Review::where('user_id', $this->user->id)->first();
        $this->assertEquals(2, $review->attachments()->count());

        $attachments = $review->attachments()->get();
        $this->assertEquals('/images/product1.jpg', $attachments[0]->file_path);
        $this->assertEquals('/images/product2.png', $attachments[1]->file_path);
    }

    public function test_review_response_includes_image_data()
    {
        // Create a completed order for the user with this product
        $this->createCompletedOrder();
        Passport::actingAs($this->user);

        $imagePaths = [
            '/images/test.jpg',
        ];

        $response = $this->postJson('/api/client/reviews', [
            'product_id' => $this->product->id,
            'rating' => 4,
            'comment' => 'Good product!',
            'images' => $imagePaths,
        ]);

        $response->assertStatus(201);

        // Check the response structure includes images
        $response->assertJsonStructure([
            'data' => [
                'id',
                'rating',
                'comment',
                'images' => [
                    '*' => [
                        'id',
                        'path',
                    ]
                ]
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertCount(1, $responseData['images']);
        $this->assertEquals('/images/test.jpg', $responseData['images'][0]['path']);

        // Also verify in database
        $review = Review::where('user_id', $this->user->id)->first();
        $this->assertNotNull($review);
        $this->assertEquals(1, $review->attachments()->count());
    }

    private function createCompletedOrder()
    {
        $order = Order::create([
            'user_id' => $this->user->id,
            'order_number' => 'ORD-' . time(),
            'total_amount' => 100.00,
            'payment_status' => 'paid',
            'fulfillment_status' => 'delivered',
            'status' => 'completed',
        ]);

        OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'product_title' => $this->product->title_en,
            'quantity' => 1,
            'price' => 100.00,
            'base_price' => 100.00,
            'total' => 100.00,
            'product_snapshot' => [
                'name' => $this->product->title_en,
                'price' => 100.00,
            ],
        ]);

        return $order;
    }
}
