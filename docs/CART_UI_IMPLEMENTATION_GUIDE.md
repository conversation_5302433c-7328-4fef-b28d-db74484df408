# Shopping Cart API - Complete Implementation Guide

## 🎯 Overview

This guide provides a complete, organized reference for implementing shopping cart functionality in your React app. It covers both **guest users** and **authenticated users** with clear examples and proper cart merging strategies.

## 🚀 Key Features

- ✅ **Token-based authentication** - Works with React/mobile apps (NEW)
- ✅ **Session-based fallback** - Backward compatibility
- ✅ **Guest cart support** - No login required
- ✅ **Automatic cart migration** - Guest → User when logging in
- ✅ **Multi-vendor support** - Automatic cart splitting
- ✅ **Real-time validation** - Stock, pricing, coupons
- ✅ **Cart recovery** - Abandoned cart emails

## 🔧 Authentication Methods

### Method 1: Token-Based (Recommended for React)
```javascript
// Headers for guest users
{
  'X-Cart-Token': 'your-cart-token-here',
  'Accept': 'application/json'
}

// Headers for authenticated users
{
  'Authorization': 'Bearer your-jwt-token',
  'Accept': 'application/json'
}
```

### Method 2: Session-Based (Legacy)
```javascript
// Relies on cookies/sessions (not recommended for React)
{
  'Accept': 'application/json'
}
```

---

# 👤 GUEST USER APIS

## 1. Create Guest Cart

**Purpose:** Start shopping as a guest user

```http
POST /api/client/cart
```

**Request:**
```json
{
  "currency": "USD",
  "notes": "Guest shopping cart"
}
```

**Response:**
```json
{
  "status": true,
  "message": "Cart created successfully!",
  "data": {
    "uuid": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
    "cart_token": "abc123def456...", // 👈 Store this in localStorage!
    "currency": "USD",
    "items": [],
    "total_amount": 0,
    "items_count": 0
  }
}
```

**React Implementation:**
```javascript
const createGuestCart = async () => {
  const response = await fetch('/api/client/cart', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ currency: 'USD' })
  });
  
  const result = await response.json();
  
  // Store credentials
  localStorage.setItem('cart_id', result.data.uuid);
  localStorage.setItem('cart_token', result.data.cart_token);
  
  return result.data;
};
```

## 2. Get Guest Cart

**Purpose:** Retrieve guest cart contents

```http
GET /api/client/cart/{cartId}
```

**Headers:**
```
X-Cart-Token: abc123def456...
Accept: application/json
```

**Response:**
```json
{
  "status": true,
  "data": {
    "uuid": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
    "cart_token": "abc123def456...",
    "currency": "USD",
    "items": [
      {
        "id": 456,
        "product_id": 789,
        "quantity": 2,
        "unit_price": 100.00,
        "total_price": 200.00,
        "product": {
          "title_en": "Premium Vitamin D3",
          "image_url": "https://cdn.example.com/product.jpg"
        }
      }
    ],
    "subtotal": 200.00,
    "tax_amount": 10.00,
    "total_amount": 210.00,
    "items_count": 1,
    "total_quantity": 2
  }
}
```

**React Implementation:**
```javascript
const getGuestCart = async () => {
  const cartId = localStorage.getItem('cart_id');
  const cartToken = localStorage.getItem('cart_token');
  
  const response = await fetch(`/api/client/cart/${cartId}`, {
    headers: {
      'X-Cart-Token': cartToken,
      'Accept': 'application/json'
    }
  });
  
  return await response.json();
};
```

## 3. Add Item to Guest Cart

**Purpose:** Add products to guest cart

```http
POST /api/client/cart/{cartId}/items
```

**Headers:**
```
X-Cart-Token: abc123def456...
Content-Type: application/json
```

**Request:**
```json
{
  "product_id": "product-uuid-here",
  "quantity": 2,
  "variant_id": "variant-uuid-here"
}
```

**Response:**
```json
{
  "status": true,
  "message": "Item added to cart successfully!",
  "data": {
    "cart_item": {
      "id": 456,
      "product_id": 789,
      "quantity": 2,
      "unit_price": 100.00,
      "total_price": 200.00
    },
    "cart_totals": {
      "subtotal": 200.00,
      "tax_amount": 10.00,
      "total_amount": 210.00,
      "items_count": 1,
      "total_quantity": 2
    }
  }
}
```

## 4. Update Guest Cart Item

**Purpose:** Change quantity or remove items

```http
PUT /api/client/cart/{cartId}/items/{itemId}
```

**Headers:**
```
X-Cart-Token: abc123def456...
Content-Type: application/json
```

**Request:**
```json
{
  "quantity": 5
}
```

**Response:**
```json
{
  "status": true,
  "message": "Cart item updated successfully!",
  "data": {
    "cart_item": {
      "id": 456,
      "quantity": 5,
      "total_price": 500.00
    },
    "cart_totals": {
      "subtotal": 500.00,
      "tax_amount": 25.00,
      "total_amount": 525.00
    }
  }
}
```

## 5. Remove Item from Guest Cart

**Purpose:** Delete specific item

```http
DELETE /api/client/cart/{cartId}/items/{itemId}
```

**Headers:**
```
X-Cart-Token: abc123def456...
```

**Response:**
```json
{
  "status": true,
  "message": "Item removed from cart successfully!",
  "data": {
    "cart_totals": {
      "subtotal": 0.00,
      "tax_amount": 0.00,
      "total_amount": 0.00,
      "items_count": 0
    }
  }
}
```

## 6. Clear Guest Cart

**Purpose:** Remove all items

```http
DELETE /api/client/cart/{cartId}
```

**Headers:**
```
X-Cart-Token: abc123def456...
```

**Response:**
```json
{
  "status": true,
  "message": "Cart cleared successfully!",
  "data": {
    "cart_totals": {
      "subtotal": 0.00,
      "total_amount": 0.00,
      "items_count": 0
    }
  }
}
```

## 7. Apply Coupon to Guest Cart

**Purpose:** Add discount codes

```http
POST /api/client/cart/{cartId}/apply-coupon
```

**Headers:**
```
X-Cart-Token: abc123def456...
Content-Type: application/json
```

**Request:**
```json
{
  "coupon_code": "SAVE20"
}
```

**Response:**
```json
{
  "status": true,
  "message": "Coupon applied successfully!",
  "data": {
    "coupon": {
      "code": "SAVE20",
      "discount_amount": 25.00,
      "description": "10% off your order"
    },
    "cart_totals": {
      "subtotal": 250.00,
      "discount_amount": 25.00,
      "total_amount": 237.50
    }
  }
}
```

## 8. Get Guest Cart Vendor Split

**Purpose:** See cart grouped by vendors (for checkout)

```http
GET /api/client/cart/{cartId}/vendors
```

**Headers:**
```
X-Cart-Token: abc123def456...
```

**Response:**
```json
{
  "status": true,
  "data": {
    "vendor_groups": [
      {
        "vendor_id": 12,
        "vendor_name": "Health Plus Store",
        "items_count": 2,
        "subtotal": 300.00,
        "tax_amount": 15.00,
        "shipping_amount": 25.00,
        "total_amount": 340.00,
        "items": [
          {
            "id": 456,
            "product_id": 789,
            "quantity": 2,
            "unit_price": 100.00,
            "product": {
              "title_en": "Premium Vitamin D3"
            }
          }
        ]
      }
    ],
    "summary": {
      "total_vendors": 1,
      "total_items": 2,
      "subtotal": 300.00,
      "tax_amount": 15.00,
      "total_amount": 340.00
    }
  }
}
```

---

# 🔐 AUTHENTICATED USER APIS

## 1. Get Current User Cart

**Purpose:** Get the logged-in user's cart

```http
GET /api/client/my-cart
```

**Headers:**
```
Authorization: Bearer your-jwt-token
Accept: application/json
```

**Response:**
```json
{
  "status": true,
  "data": {
    "uuid": "user-cart-uuid",
    "user_id": 456,
    "currency": "USD",
    "items": [
      {
        "id": 456,
        "product_id": 789,
        "quantity": 2,
        "unit_price": 100.00,
        "total_price": 200.00,
        "product": {
          "title_en": "Premium Vitamin D3"
        }
      }
    ],
    "subtotal": 200.00,
    "tax_amount": 10.00,
    "total_amount": 210.00,
    "items_count": 1,
    "total_quantity": 2
  }
}
```

**React Implementation:**
```javascript
const getUserCart = async () => {
  const token = localStorage.getItem('jwt_token');
  
  const response = await fetch('/api/client/my-cart', {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Accept': 'application/json'
    }
  });
  
  return await response.json();
};
```

## 2. Migrate Guest Cart to User

**Purpose:** Merge guest cart with user cart when logging in

```http
POST /api/client/my-cart/migrate
```

**Headers:**
```
Authorization: Bearer your-jwt-token
Content-Type: application/json
```

**Request:**
```json
{
  "guest_session_id": "guest-session-uuid",
  "merge_strategy": "merge",
  "clear_guest_cart": true
}
```

**Merge Strategies:**
- `merge`: Combine items, increase quantities for duplicates
- `replace`: Replace user cart with guest cart items
- `keep_both`: Keep all items separately

**Response:**
```json
{
  "status": true,
  "message": "Guest cart migrated successfully!",
  "data": {
    "migration_result": {
      "items_migrated": 3,
      "items_merged": 1,
      "items_skipped": 0,
      "guest_cart_cleared": true
    },
    "cart": {
      "uuid": "user-cart-uuid",
      "items_count": 4,
      "total_amount": 520.00,
      "items": [...]
    }
  }
}
```

**React Implementation:**
```javascript
const migrateGuestCart = async () => {
  const guestSessionId = localStorage.getItem('guest_session_id');
  const token = localStorage.getItem('jwt_token');
  
  const response = await fetch('/api/client/my-cart/migrate', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      guest_session_id: guestSessionId,
      merge_strategy: 'merge',
      clear_guest_cart: true
    })
  });
  
  // Clear guest cart data after migration
  localStorage.removeItem('cart_id');
  localStorage.removeItem('cart_token');
  localStorage.removeItem('guest_session_id');
  
  return await response.json();
};
```

## 3. Get User Cart History

**Purpose:** View previous carts and orders

```http
GET /api/client/my-cart/history
```

**Headers:**
```
Authorization: Bearer your-jwt-token
```

**Query Parameters:**
- `status`: Filter by status (`active`, `abandoned`, `completed`)
- `limit`: Number of results (default: 20)
- `offset`: Pagination offset

**Response:**
```json
{
  "status": true,
  "data": {
    "carts": [
      {
        "uuid": "old-cart-uuid",
        "status": "abandoned",
        "items_count": 2,
        "total_amount": 200.00,
        "last_activity_at": "2024-07-25T14:20:00Z",
        "created_at": "2024-07-25T10:00:00Z"
      }
    ],
    "pagination": {
      "total": 15,
      "limit": 20,
      "offset": 0,
      "has_more": false
    }
  }
}
```

## 4. Save Items for Later

**Purpose:** Move cart items to wishlist

```http
POST /api/client/my-cart/save-for-later
```

**Headers:**
```
Authorization: Bearer your-jwt-token
Content-Type: application/json
```

**Request:**
```json
{
  "cart_item_ids": [456, 457, 458]
}
```

**Response:**
```json
{
  "status": true,
  "message": "Items saved for later!",
  "data": {
    "saved_items": [
      {
        "id": 789,
        "product_id": 123,
        "quantity": 2,
        "unit_price": 100.00,
        "saved_at": "2024-08-01T10:30:00Z",
        "product": {
          "title_en": "Premium Vitamin D3"
        }
      }
    ],
    "cart_totals": {
      "items_count": 0,
      "total_amount": 0.00
    }
  }
}
```

## 5. Get Saved Items

**Purpose:** View wishlist items

```http
GET /api/client/my-cart/saved-items
```

**Headers:**
```
Authorization: Bearer your-jwt-token
```

**Response:**
```json
{
  "status": true,
  "data": {
    "saved_items": [
      {
        "id": 789,
        "product_id": 123,
        "quantity": 2,
        "unit_price": 100.00,
        "saved_at": "2024-08-01T10:30:00Z",
        "product": {
          "title_en": "Premium Vitamin D3",
          "current_price": 95.00,
          "is_available": true
        }
      }
    ],
    "pagination": {
      "total": 5,
      "limit": 20,
      "offset": 0
    }
  }
}
```

## 6. Get User Cart Statistics

**Purpose:** View shopping analytics

```http
GET /api/client/my-cart/statistics
```

**Headers:**
```
Authorization: Bearer your-jwt-token
```

**Response:**
```json
{
  "status": true,
  "data": {
    "total_carts_created": 25,
    "completed_purchases": 18,
    "abandoned_carts": 7,
    "average_cart_value": 285.50,
    "cart_conversion_rate": 72.0,
    "favorite_categories": [
      {
        "category_name": "Vitamins",
        "purchase_count": 45
      }
    ]
  }
}
```

---

# 🔄 CART MERGING & MIGRATION

## How Cart Merging Works

When a guest user logs in, their guest cart needs to be merged with their user account cart. Here's how it works:

### Scenario 1: Guest Cart + Empty User Cart
```
Guest Cart: [Product A (qty: 2), Product B (qty: 1)]
User Cart:  []
Result:     [Product A (qty: 2), Product B (qty: 1)]
```

### Scenario 2: Guest Cart + Existing User Cart (Different Products)
```
Guest Cart: [Product A (qty: 2), Product B (qty: 1)]
User Cart:  [Product C (qty: 3)]
Result:     [Product A (qty: 2), Product B (qty: 1), Product C (qty: 3)]
```

### Scenario 3: Guest Cart + Existing User Cart (Same Products)
```
Guest Cart: [Product A (qty: 2), Product B (qty: 1)]
User Cart:  [Product A (qty: 1), Product C (qty: 3)]

With "merge" strategy:
Result:     [Product A (qty: 3), Product B (qty: 1), Product C (qty: 3)]

With "replace" strategy:
Result:     [Product A (qty: 2), Product B (qty: 1)]
```

## React Implementation for Login Flow

```javascript
const handleLogin = async (credentials) => {
  // 1. Login user
  const loginResponse = await login(credentials);
  const token = loginResponse.data.token;
  localStorage.setItem('jwt_token', token);
  
  // 2. Check if guest cart exists
  const guestCartId = localStorage.getItem('cart_id');
  const guestCartToken = localStorage.getItem('cart_token');
  
  if (guestCartId && guestCartToken) {
    // 3. Migrate guest cart to user account
    try {
      await migrateGuestCart();
      console.log('✅ Guest cart migrated successfully');
    } catch (error) {
      console.error('❌ Cart migration failed:', error);
    }
  }
  
  // 4. Load user cart
  const userCart = await getUserCart();
  return userCart;
};
```

---

# 🛒 SHARED CART OPERATIONS

These APIs work for both guest and authenticated users. Just use the appropriate headers:

## 1. Bulk Update Items

**Purpose:** Update multiple items at once

```http
POST /api/client/cart/{cartId}/items/bulk
```

**Headers (Guest):**
```
X-Cart-Token: abc123def456...
Content-Type: application/json
```

**Headers (User):**
```
Authorization: Bearer your-jwt-token
Content-Type: application/json
```

**Request:**
```json
{
  "items": [
    {"id": 456, "quantity": 3},
    {"id": 457, "quantity": 0},
    {"id": 458, "quantity": 1}
  ]
}
```

## 2. Validate Cart

**Purpose:** Check stock, prices, and coupons before checkout

```http
POST /api/client/cart/{cartId}/validate
```

**Response:**
```json
{
  "status": true,
  "data": {
    "is_valid": true,
    "validation_results": {
      "stock_validation": {
        "all_available": true,
        "unavailable_items": []
      },
      "price_validation": {
        "prices_current": true,
        "price_changes": []
      },
      "coupon_validation": {
        "coupons_valid": true,
        "invalid_coupons": []
      }
    }
  }
}
```

## 3. Merge Carts

**Purpose:** Combine two carts (used internally for migration)

```http
POST /api/client/cart/{cartId}/merge
```

**Request:**
```json
{
  "source_cart_id": "source-cart-uuid",
  "merge_strategy": "merge",
  "clear_source_cart": true
}
```

---

# 🔄 CART RECOVERY APIS

## 1. Recover Abandoned Cart

**Purpose:** Access cart via email recovery link

```http
GET /api/client/cart/recover/{token}
```

**Response:**
```json
{
  "status": true,
  "data": {
    "cart": {
      "uuid": "abandoned-cart-uuid",
      "items_count": 3,
      "total_amount": 450.00,
      "abandoned_at": "2024-07-25T10:30:00Z",
      "items": [...]
    },
    "recovery_token": "recovery-token-here",
    "expires_at": "2024-08-15T10:30:00Z"
  }
}
```

## 2. Send Cart Recovery Email

**Purpose:** Send abandoned cart reminder

```http
POST /api/client/cart-recovery/send-reminder
```

**Request:**
```json
{
  "email": "<EMAIL>",
  "cart_id": "cart-uuid-here"
}
```

---

# 📱 REACT IMPLEMENTATION EXAMPLES

## Complete Guest Cart Service

```javascript
// services/guestCartService.js
class GuestCartService {
  constructor() {
    this.baseURL = '/api/client';
    this.cartToken = localStorage.getItem('cart_token');
    this.cartId = localStorage.getItem('cart_id');
  }

  async createCart() {
    const response = await fetch(`${this.baseURL}/cart`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ currency: 'USD' })
    });

    const result = await response.json();
    
    if (result.status) {
      this.cartId = result.data.uuid;
      this.cartToken = result.data.cart_token;
      
      localStorage.setItem('cart_id', this.cartId);
      localStorage.setItem('cart_token', this.cartToken);
    }
    
    return result;
  }

  async getCart() {
    if (!this.cartId || !this.cartToken) {
      return await this.createCart();
    }

    const response = await fetch(`${this.baseURL}/cart/${this.cartId}`, {
      headers: {
        'X-Cart-Token': this.cartToken,
        'Accept': 'application/json'
      }
    });

    const result = await response.json();
    
    if (!result.status) {
      // Cart not found, create new one
      return await this.createCart();
    }
    
    return result;
  }

  async addItem(productId, quantity = 1, variantId = null) {
    if (!this.cartId) await this.createCart();

    const response = await fetch(`${this.baseURL}/cart/${this.cartId}/items`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Cart-Token': this.cartToken
      },
      body: JSON.stringify({
        product_id: productId,
        quantity,
        variant_id: variantId
      })
    });

    return await response.json();
  }
}

export default new GuestCartService();
```

## Complete User Cart Service

```javascript
// services/userCartService.js
class UserCartService {
  constructor() {
    this.baseURL = '/api/client';
  }

  getAuthHeaders() {
    const token = localStorage.getItem('jwt_token');
    return {
      'Authorization': `Bearer ${token}`,
      'Accept': 'application/json'
    };
  }

  async getCurrentCart() {
    const response = await fetch(`${this.baseURL}/my-cart`, {
      headers: this.getAuthHeaders()
    });

    return await response.json();
  }

  async migrateGuestCart() {
    const guestSessionId = localStorage.getItem('guest_session_id');
    
    if (!guestSessionId) return null;

    const response = await fetch(`${this.baseURL}/my-cart/migrate`, {
      method: 'POST',
      headers: {
        ...this.getAuthHeaders(),
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        guest_session_id: guestSessionId,
        merge_strategy: 'merge',
        clear_guest_cart: true
      })
    });

    const result = await response.json();
    
    if (result.status) {
      // Clear guest cart data
      localStorage.removeItem('cart_id');
      localStorage.removeItem('cart_token');
      localStorage.removeItem('guest_session_id');
    }
    
    return result;
  }

  async getCartHistory(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = `${this.baseURL}/my-cart/history${queryString ? '?' + queryString : ''}`;
    
    const response = await fetch(url, {
      headers: this.getAuthHeaders()
    });

    return await response.json();
  }
}

export default new UserCartService();
```

## React Hook for Cart Management

```javascript
// hooks/useCart.js
import { useState, useEffect } from 'react';
import guestCartService from '../services/guestCartService';
import userCartService from '../services/userCartService';

export const useCart = () => {
  const [cart, setCart] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const isAuthenticated = !!localStorage.getItem('jwt_token');

  useEffect(() => {
    loadCart();
  }, [isAuthenticated]);

  const loadCart = async () => {
    try {
      setLoading(true);
      setError(null);
      
      let result;
      if (isAuthenticated) {
        result = await userCartService.getCurrentCart();
      } else {
        result = await guestCartService.getCart();
      }
      
      if (result.status) {
        setCart(result.data);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const addToCart = async (productId, quantity = 1, variantId = null) => {
    try {
      setLoading(true);
      
      let result;
      if (isAuthenticated) {
        // For authenticated users, use regular cart operations
        // (same endpoints, just different headers)
        result = await guestCartService.addItem(productId, quantity, variantId);
      } else {
        result = await guestCartService.addItem(productId, quantity, variantId);
      }
      
      if (result.status) {
        await loadCart(); // Reload cart
      }
      
      return result;
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleLogin = async () => {
    // After successful login, migrate guest cart
    if (!isAuthenticated) {
      try {
        await userCartService.migrateGuestCart();
        await loadCart(); // Reload user cart
      } catch (err) {
        console.error('Cart migration failed:', err);
      }
    }
  };

  return {
    cart,
    loading,
    error,
    loadCart,
    addToCart,
    handleLogin
  };
};
```

---

# 🎯 QUICK START CHECKLIST

## For Guest Users:
1. ✅ Create cart → Get `cart_token`
2. ✅ Store `cart_token` in localStorage
3. ✅ Send `X-Cart-Token` header in all requests
4. ✅ Use `/api/client/cart/{cartId}/*` endpoints

## For Authenticated Users:
1. ✅ Login → Get JWT token
2. ✅ Send `Authorization: Bearer {token}` header
3. ✅ Use `/api/client/my-cart/*` endpoints
4. ✅ Migrate guest cart on login

## Cart Migration Flow:
1. ✅ User shops as guest (cart_token stored)
2. ✅ User logs in (JWT token received)
3. ✅ Call `/api/client/my-cart/migrate` with guest session
4. ✅ Clear guest cart data from localStorage
5. ✅ Load user cart with merged items

---

# 🔧 ERROR HANDLING

## Common Error Responses

### 422 Validation Error
```json
{
  "status": false,
  "message": "Cart ID must be a valid UUID format.",
  "errors": {
    "cartId": ["Cart ID must be a valid UUID format."]
  }
}
```

### 403 Unauthorized
```json
{
  "status": false,
  "message": "Unauthorized access to cart",
  "errors": "Unauthorized"
}
```

### 404 Not Found
```json
{
  "status": false,
  "message": "Cart not found",
  "errors": "Cart not found"
}
```

## React Error Handling

```javascript
const handleApiCall = async (apiCall) => {
  try {
    const result = await apiCall();
    
    if (!result.status) {
      // Handle API errors
      if (result.errors) {
        console.error('Validation errors:', result.errors);
      }
      throw new Error(result.message || 'API call failed');
    }
    
    return result.data;
  } catch (error) {
    console.error('API Error:', error);
    throw error;
  }
};
```

---

# 🚀 DEPLOYMENT CHECKLIST

## Backend Setup:
- [x] Run migration: `php artisan migrate`
- [x] Token-based authentication implemented
- [x] Cart validation added
- [x] Error handling improved
- [ ] Rate limiting configured
- [ ] Email service for cart recovery

## Frontend Setup:
- [ ] Implement guest cart service
- [ ] Implement user cart service
- [ ] Add cart migration on login
- [ ] Handle token storage
- [ ] Add error handling
- [ ] Test cart merging scenarios

This guide provides everything you need to implement a complete shopping cart system that works perfectly with React apps while maintaining backward compatibility with session-based systems.